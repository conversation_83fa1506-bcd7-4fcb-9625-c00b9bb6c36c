<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Badge</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 50px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .badge-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .badge:hover::before {
            left: 100%;
        }

        .badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .badge.success {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
        }

        .badge.warning {
            background: linear-gradient(45deg, #ff9800, #f57c00);
            color: white;
        }

        .badge.error {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }

        .badge.info {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            color: white;
        }

        .badge.primary {
            background: linear-gradient(45deg, #9c27b0, #7b1fa2);
            color: white;
        }

        .badge-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .badge-count {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            padding: 2px 8px;
            margin-left: 8px;
            font-size: 12px;
            font-weight: bold;
        }

        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-top: 20px;
        }

        .controls h3 {
            margin-top: 0;
            color: #333;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 600;
        }

        .control-group input, .control-group select {
            width: 100%;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }

        .control-group input:focus, .control-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: 600;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            background: #4CAF50;
        }

        .notification.error {
            background: #f44336;
        }
    </style>
</head>
<body>
    <h1 style="color: white; text-align: center; margin-bottom: 30px;">Interactive Badge System</h1>

    <div class="badge-container" id="badgeContainer">
        <div class="badge success" onclick="badgeClick(this)">
            <span class="badge-icon">✓</span>
            Success
            <span class="badge-count">5</span>
        </div>

        <div class="badge warning" onclick="badgeClick(this)">
            <span class="badge-icon">⚠</span>
            Warning
            <span class="badge-count">2</span>
        </div>

        <div class="badge error" onclick="badgeClick(this)">
            <span class="badge-icon">✗</span>
            Error
            <span class="badge-count">1</span>
        </div>

        <div class="badge info" onclick="badgeClick(this)">
            <span class="badge-icon">ℹ</span>
            Info
            <span class="badge-count">8</span>
        </div>

        <div class="badge primary" onclick="badgeClick(this)">
            <span class="badge-icon">★</span>
            Featured
            <span class="badge-count">3</span>
        </div>
    </div>

    <div class="controls">
        <h3>Create Custom Badge</h3>

        <div class="control-group">
            <label for="badgeText">Badge Text:</label>
            <input type="text" id="badgeText" placeholder="Enter badge text" value="Custom">
        </div>

        <div class="control-group">
            <label for="badgeType">Badge Type:</label>
            <select id="badgeType">
                <option value="success">Success</option>
                <option value="warning">Warning</option>
                <option value="error">Error</option>
                <option value="info">Info</option>
                <option value="primary">Primary</option>
            </select>
        </div>

        <div class="control-group">
            <label for="badgeIcon">Icon:</label>
            <input type="text" id="badgeIcon" placeholder="Enter icon (emoji or symbol)" value="🎯">
        </div>

        <div class="control-group">
            <label for="badgeCount">Count:</label>
            <input type="number" id="badgeCount" placeholder="Enter count" value="1" min="0">
        </div>

        <button class="btn" onclick="createBadge()">Create Badge</button>
        <button class="btn" onclick="clearBadges()" style="background: linear-gradient(45deg, #f44336, #d32f2f); margin-left: 10px;">Clear All</button>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        // Badge click handler
        function badgeClick(badge) {
            const text = badge.textContent.trim();
            const type = badge.className.split(' ')[1];

            // Add click animation
            badge.style.transform = 'scale(0.95)';
            setTimeout(() => {
                badge.style.transform = '';
            }, 150);

            showNotification(`Clicked ${text} badge!`, 'success');

            // Increment count
            const countElement = badge.querySelector('.badge-count');
            if (countElement) {
                let currentCount = parseInt(countElement.textContent);
                countElement.textContent = currentCount + 1;
            }
        }

        // Create new badge
        function createBadge() {
            const text = document.getElementById('badgeText').value.trim();
            const type = document.getElementById('badgeType').value;
            const icon = document.getElementById('badgeIcon').value.trim();
            const count = document.getElementById('badgeCount').value;

            if (!text) {
                showNotification('Please enter badge text!', 'error');
                return;
            }

            const badgeContainer = document.getElementById('badgeContainer');

            const newBadge = document.createElement('div');
            newBadge.className = `badge ${type}`;
            newBadge.onclick = function() { badgeClick(this); };

            newBadge.innerHTML = `
                ${icon ? `<span class="badge-icon">${icon}</span>` : ''}
                ${text}
                ${count ? `<span class="badge-count">${count}</span>` : ''}
            `;

            badgeContainer.appendChild(newBadge);

            // Add entrance animation
            newBadge.style.opacity = '0';
            newBadge.style.transform = 'scale(0.5)';
            setTimeout(() => {
                newBadge.style.transition = 'all 0.3s ease';
                newBadge.style.opacity = '1';
                newBadge.style.transform = 'scale(1)';
            }, 10);

            showNotification('Badge created successfully!', 'success');

            // Reset form
            document.getElementById('badgeText').value = 'Custom';
            document.getElementById('badgeIcon').value = '🎯';
            document.getElementById('badgeCount').value = '1';
        }

        // Clear all badges except the default ones
        function clearBadges() {
            const badgeContainer = document.getElementById('badgeContainer');
            const badges = badgeContainer.querySelectorAll('.badge');

            // Keep only the first 5 default badges
            for (let i = badges.length - 1; i >= 5; i--) {
                badges[i].style.transition = 'all 0.3s ease';
                badges[i].style.opacity = '0';
                badges[i].style.transform = 'scale(0)';
                setTimeout(() => {
                    if (badges[i] && badges[i].parentNode) {
                        badges[i].remove();
                    }
                }, 300);
            }

            showNotification('Custom badges cleared!', 'success');
        }

        // Show notification
        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Add keyboard support
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && e.target.id === 'badgeText') {
                createBadge();
            }
        });

        // Add some initial animation
        window.addEventListener('load', function() {
            const badges = document.querySelectorAll('.badge');
            badges.forEach((badge, index) => {
                badge.style.opacity = '0';
                badge.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    badge.style.transition = 'all 0.5s ease';
                    badge.style.opacity = '1';
                    badge.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>

